"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { MapPin, Globe, Flag } from "lucide-react"

export type LocationPreference = 'local' | 'national' | 'global'

interface LocationPreferenceToggleProps {
  value: LocationPreference
  onChange: (preference: LocationPreference) => void
  disabled?: boolean
  hasUserLocation?: boolean
}

export function LocationPreferenceToggle({ 
  value, 
  onChange, 
  disabled = false,
  hasUserLocation = false 
}: LocationPreferenceToggleProps) {
  const options = [
    {
      value: 'local' as const,
      label: 'Local',
      description: '100 miles or less',
      icon: MapPin,
      disabled: !hasUserLocation,
    },
    {
      value: 'national' as const,
      label: 'National',
      description: 'Within my country',
      icon: Flag,
      disabled: !hasUserLocation,
    },
    {
      value: 'global' as const,
      label: 'Global',
      description: 'Anywhere in the world',
      icon: Globe,
      disabled: false,
    },
  ]

  return (
    <div className="flex flex-col sm:flex-row gap-2">
      {options.map((option) => {
        const Icon = option.icon
        const isSelected = value === option.value
        const isDisabled = disabled || option.disabled

        return (
          <Button
            key={option.value}
            variant={isSelected ? "default" : "outline"}
            size="sm"
            onClick={() => !isDisabled && onChange(option.value)}
            disabled={isDisabled}
            className={`
              flex flex-col items-center gap-1 h-auto py-2 px-3 min-w-[80px]
              ${isSelected ? 'bg-primary text-primary-foreground' : ''}
              ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
            title={option.disabled && !hasUserLocation ? 'Setup your location to use this option' : undefined}
          >
            <Icon className="h-4 w-4" />
            <div className="text-center">
              <div className="text-xs font-medium">{option.label}</div>
              <div className="text-xs opacity-75 hidden sm:block">{option.description}</div>
            </div>
          </Button>
        )
      })}
    </div>
  )
}
