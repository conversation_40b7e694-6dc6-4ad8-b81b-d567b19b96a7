#!/usr/bin/env tsx

/**
 * Simple Migration Script for Flat Subscription Architecture
 *
 * This script migrates existing subscription data to the new flat collection structure.
 *
 * Usage:
 *   npm run migrate:flat-subscriptions:simple [options] [env-file]
 *
 * Options:
 *   --dry-run          Preview migration without making changes
 *   --validate-only    Only validate current migration state
 *   --backup-only      Only backup existing data
 *   --rollback         Rollback to previous state
 *   --retry-failed     Retry migration for users with old structure
 *   --user=USER_ID     Migrate specific user (can be used multiple times)
 *
 * Examples:
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts --dry-run
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts --validate-only
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts --retry-failed
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts --user=user123 --user=user456
 *   tsx scripts/migrate-to-flat-subscriptions-simple.ts .env.production
 */

import { config } from "dotenv"
import { resolve } from "path"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { AdminMigrationService } from "../lib/domains/user-subscription/admin-migration.service"

// Parse command line arguments
const args = process.argv.slice(2)
const isDryRun = args.includes("--dry-run")
const isValidateOnly = args.includes("--validate-only")
const isBackupOnly = args.includes("--backup-only")
const isRollback = args.includes("--rollback")
const isRetryFailed = args.includes("--retry-failed")
const specificUserIds = args
  .filter((arg) => arg.startsWith("--user="))
  .map((arg) => arg.replace("--user=", ""))
const envFile =
  args.find((arg) => !arg.startsWith("--") && !arg.startsWith("--user=")) || ".env.vercel"

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  // Load environment variables from specified .env file
  const envPath = resolve(process.cwd(), envFile)
  console.log(`Loading environment variables from: ${envPath}`)
  config({ path: envPath })

  // Initialize Firebase Admin SDK if not already initialized
  if (!getApps().length) {
    try {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      initializeApp({
        credential: cert(serviceAccount),
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      })

      console.log("✅ Firebase Admin SDK initialized successfully")
    } catch (error) {
      console.error("❌ Firebase admin initialization error:", error)
      process.exit(1)
    }
  }

  return getFirestore()
}

async function performBackupOnly() {
  console.log("📦 Backing up existing subscription data...\n")

  const result = await AdminMigrationService.backupExistingData()

  if (result.success) {
    const { backedUpCount, errors } = result.data!
    console.log(`✅ Backup completed: ${backedUpCount} subscriptions backed up`)

    if (errors.length > 0) {
      console.log("⚠️  Backup warnings:")
      errors.forEach((error: string) => console.log(`  - ${error}`))
    }
  } else {
    console.error("❌ Backup failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }
}

async function validateMigration() {
  console.log("🔍 Validating flat subscription structure...\n")

  const result = await AdminMigrationService.validateMigration()

  if (!result.success) {
    console.error("❌ Validation failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }

  const { isValid, issues, statistics } = result.data!

  console.log("📊 Migration Statistics:")
  console.log(`  Total users: ${statistics.totalUsers}`)
  console.log(`  Users with subscriptions: ${statistics.usersWithSubscriptions}`)
  console.log(`  Total subscription entries: ${statistics.totalEntries}`)
  console.log(`  Free entries: ${statistics.freeEntries}`)
  console.log(`  Stripe entries: ${statistics.stripeEntries}`)
  console.log(`  Perk entries: ${statistics.perkEntries}`)
  console.log(`  Giveaway entries: ${statistics.giveawayEntries}`)
  console.log(`  Old structure entries: ${statistics.oldStructureEntries}`)
  console.log(`  Users without subscription entries: ${statistics.usersWithoutFreeEntry}`)

  if (isValid) {
    console.log("\n✅ Migration validation PASSED")
  } else {
    console.log("\n❌ Migration validation FAILED")
    console.log("\n🚨 Issues found:")
    issues.forEach((issue: string, index: number) => {
      console.log(`  ${index + 1}. ${issue}`)
    })
    process.exit(1)
  }
}

async function performRollback() {
  console.log("🚨 EMERGENCY ROLLBACK")
  console.log("This will restore the original subscription structure!")
  console.log("⚠️  All new flat subscription data will be DELETED!")

  // Confirm before proceeding
  console.log("Press Ctrl+C to cancel, or wait 10 seconds to continue...")
  await new Promise((resolve) => setTimeout(resolve, 10000))

  const result = await AdminMigrationService.emergencyRollback()

  if (result.success) {
    const { restoredCount, deletedCount, errors } = result.data!
    console.log(`✅ Rollback completed:`)
    console.log(`  📦 Restored: ${restoredCount} original subscriptions`)
    console.log(`  🗑️  Deleted: ${deletedCount} flat entries`)

    if (errors.length > 0) {
      console.log("⚠️  Rollback warnings:")
      errors.forEach((error: string) => console.log(`  - ${error}`))
    }
  } else {
    console.error("❌ Rollback failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }
}

async function runDryRun() {
  console.log("🔍 [DRY RUN] Full migration preview\n")
  console.log("This is a dry run - no changes will be made")

  console.log("Would perform the following actions:")
  console.log("1. 📦 Backup existing subscription data")
  console.log("2. 🔄 Convert subscription documents to flat entries")
  console.log("3. ➕ Ensure all users have free subscription entries")
  console.log("4. 🗑️  Delete original subscription documents")
  console.log("5. 🔍 Validate migration results")

  console.log("\nTo perform the actual migration, run without --dry-run flag")
}

// Find users with old structure that need migration
async function findUsersWithOldStructure() {
  console.log("🔍 Finding users with old subscription structure...")

  const validationResult = await AdminMigrationService.validateMigration()
  if (!validationResult.success) {
    throw new Error("Failed to validate migration state")
  }

  const { statistics } = validationResult.data!
  const usersWithOldStructure: string[] = []

  if (statistics.oldStructureEntries > 0) {
    // Get all subscription documents to find old structure ones
    const db = getFirestore()
    const subscriptionsSnapshot = await db.collection("userSubscriptions").get()

    for (const doc of subscriptionsSnapshot.docs) {
      const data = doc.data()

      // Check if it's old structure (document ID matches userId and no 'source' field)
      if (doc.id === data.userId && !data.source) {
        usersWithOldStructure.push(doc.id)
      }
    }
  }

  console.log(`Found ${usersWithOldStructure.length} users with old structure:`)
  usersWithOldStructure.forEach((userId) => {
    console.log(`  - ${userId}`)
  })

  return usersWithOldStructure
}

// Migrate specific users
async function migrateSpecificUsers(userIds: string[]) {
  console.log(`🔄 Migrating ${userIds.length} specific users...`)

  let successCount = 0
  let errorCount = 0
  const errors: string[] = []

  for (const userId of userIds) {
    console.log(`\n👤 Migrating user: ${userId}`)

    try {
      const result = await AdminMigrationService.migrateUserSubscription(userId)

      if (result.success) {
        console.log(`  ✅ Success: ${result.data?.reason}`)
        successCount++
      } else {
        const errorMsg = `User ${userId}: ${(result.error as Error)?.message || result.error || "Unknown error"}`
        console.log(`  ❌ Failed: ${errorMsg}`)
        errors.push(errorMsg)
        errorCount++
      }
    } catch (error) {
      const errorMsg = `User ${userId}: ${error instanceof Error ? error.message : "Unknown error"}`
      console.log(`  ❌ Exception: ${errorMsg}`)
      errors.push(errorMsg)
      errorCount++
    }
  }

  console.log(`\n📊 Targeted migration results:`)
  console.log(`  ✅ Successful: ${successCount}`)
  console.log(`  ❌ Failed: ${errorCount}`)

  if (errors.length > 0) {
    console.log(`\n🚨 Errors:`)
    errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`)
    })
  }

  return { successCount, errorCount, errors }
}

async function runFullMigration() {
  console.log("🚀 Starting full flat subscription migration...\n")

  // Confirm before proceeding
  console.log("⚠️  This will completely restructure your subscription data!")
  console.log("⚠️  Make sure you have a database backup!")
  console.log("Press Ctrl+C to cancel, or wait 5 seconds to continue...")

  await new Promise((resolve) => setTimeout(resolve, 5000))

  console.log("\n🔄 Starting migration...")

  const migrationResult = await AdminMigrationService.migrateAllUsers()

  if (!migrationResult.success) {
    console.error(
      "❌ Migration failed:",
      (migrationResult.error as Error)?.message || migrationResult.error
    )
    process.exit(1)
  }

  const { totalProcessed, successfulMigrations, totalEntriesCreated, errors } =
    migrationResult.data!

  console.log(`\n✅ Migration completed:`)
  console.log(`  📊 Total processed: ${totalProcessed}`)
  console.log(`  ✅ Successful: ${successfulMigrations}`)
  console.log(`  📝 Entries created: ${totalEntriesCreated}`)
  console.log(`  ❌ Errors: ${errors.length}`)

  if (errors.length > 0) {
    console.log("\n🚨 Migration errors:")
    errors.forEach((error: string, index: number) => {
      console.log(`  ${index + 1}. ${error}`)
    })
  }

  console.log("\n🔍 Running validation...")
  await validateMigration()

  console.log("\n🎉 Migration completed successfully!")
  console.log("\nNext steps:")
  console.log("1. Test the application with the new subscription system")
  console.log("2. Monitor for any issues")
  console.log("3. Update your application code to use FlatSubscriptionService")
  console.log("4. Remove backup data after confirming everything works")
}

async function main() {
  try {
    // Initialize Firebase Admin SDK
    await initializeFirebase()

    console.log("🚀 Flat Subscription Migration Tool (Simple)")
    console.log("============================================")

    if (isRollback) {
      await performRollback()
      return
    }

    if (isBackupOnly) {
      await performBackupOnly()
      return
    }

    if (isValidateOnly) {
      await validateMigration()
      return
    }

    if (isDryRun) {
      await runDryRun()
      return
    }

    // Handle retry failed migrations
    if (isRetryFailed) {
      console.log("🔄 Retry Failed Migrations Mode")
      console.log("===============================")

      const usersWithOldStructure = await findUsersWithOldStructure()

      if (usersWithOldStructure.length === 0) {
        console.log("✅ No users with old structure found. Migration appears complete!")
        return
      }

      console.log(`\n⚠️  Found ${usersWithOldStructure.length} users that need migration.`)
      console.log("Press Ctrl+C to cancel, or wait 3 seconds to continue...")
      await new Promise((resolve) => setTimeout(resolve, 3000))

      await migrateSpecificUsers(usersWithOldStructure)

      console.log("\n🔍 Running validation after retry...")
      await validateMigration()
      return
    }

    // Handle specific user migrations
    if (specificUserIds.length > 0) {
      console.log("👤 Specific User Migration Mode")
      console.log("===============================")
      console.log(`Migrating users: ${specificUserIds.join(", ")}`)

      await migrateSpecificUsers(specificUserIds)

      console.log("\n🔍 Running validation after specific user migration...")
      await validateMigration()
      return
    }

    // Full migration
    await runFullMigration()
  } catch (error) {
    console.error("❌ Migration failed:", error)
    process.exit(1)
  }
}

// Handle uncaught errors
process.on("uncaughtException", (error) => {
  console.error("💥 Uncaught exception:", error)
  process.exit(1)
})

process.on("unhandledRejection", (reason, promise) => {
  console.error("💥 Unhandled rejection at:", promise, "reason:", reason)
  process.exit(1)
})

// Run the script
main().catch((error) => {
  console.error("💥 Script failed:", error)
  process.exit(1)
})
